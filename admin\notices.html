<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notices Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Quill.js CSS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/notices.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Notices Section -->
            <section id="notices-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title notice-title">Notice Management</h1>
                    <div class="section-actions notice-actions">
                        <button class="btn-import-notices" id="bulkImportBtn">
                            <i class="fas fa-upload"></i>
                            Import CSV
                        </button>
                        <button class="btn-export-notices" id="bulkExportBtn">
                            <i class="fas fa-download"></i>
                            Export CSV
                        </button>
                        <button class="btn-view-notices" id="viewAllNoticesBtn">
                            <i class="fas fa-eye"></i>
                            View All Notices
                        </button>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="notice-form-container">
                        <div class="notice-form-header">
                            <h3 class="notice-form-title">
                                <i class="fas fa-plus-circle"></i>
                                Create New Notice
                            </h3>
                        </div>
                        <div class="notice-form-content">
                            <form id="noticeForm" class="modern-form">
                                <input type="hidden" id="noticeId" name="noticeId">

                                <div class="notice-form-grid">
                                    <div class="notice-form-group">
                                        <label class="notice-form-label" for="noticeTitle">📢 Notice Title *</label>
                                        <input class="notice-form-input" type="text" id="noticeTitle" name="noticeTitle" placeholder="e.g., Important Exam Schedule Update" required minlength="5" maxlength="200">
                                    </div>

                                    <div class="notice-form-group">
                                        <label class="notice-form-label" for="publishDateTime">📅 Publish Date & Time *</label>
                                        <input class="notice-form-input" type="datetime-local" id="publishDateTime" name="publishDateTime" required>
                                    </div>

                                    <div class="notice-form-group">
                                        <label class="notice-form-label" for="expiryDate">⏰ Expiry Date (Optional)</label>
                                        <input class="notice-form-input" type="date" id="expiryDate" name="expiryDate">
                                    </div>

                                    <div class="notice-form-group">
                                        <label class="notice-form-label" for="postedBy">👤 Posted By *</label>
                                        <input class="notice-form-input" type="text" id="postedBy" name="postedBy" placeholder="Enter admin name or department" required minlength="2" maxlength="100">
                                    </div>

                                    <div class="notice-form-group" style="grid-column: 1 / -1;">
                                        <label class="notice-form-label" for="targetAudience">🎯 Target Audience *</label>
                                        <input class="notice-form-input" type="text" id="targetAudience" name="targetAudience" placeholder="e.g., All Students, BCA Students, Faculty, First Year" required minlength="3" maxlength="200">
                                        <small class="form-help">Enter target audience (e.g., "All Students", "BCA Students", "Faculty Only", "First Year Students")</small>
                                    </div>

                                    <div class="notice-form-group" style="grid-column: 1 / -1;">
                                        <label class="notice-form-label" for="noticeDescription">📝 Notice Description *</label>
                                        <div id="quillEditor" class="quill-editor"></div>
                                        <textarea id="noticeDescription" name="noticeDescription" style="display: none;" required></textarea>
                                    </div>

                                    <div class="notice-form-group" style="grid-column: 1 / -1;">
                                        <label class="notice-form-label" for="attachmentFile">📎 Attachment Upload (Optional)</label>
                                        <div class="file-upload-container">
                                            <input class="notice-form-input file-input" type="file" id="attachmentFile" name="attachmentFile" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                            <div class="file-upload-info">
                                                <small>Supported formats: PDF, Images (JPG, PNG), Word Documents. Max size: 10MB</small>
                                                <div id="uploadProgress" class="upload-progress" style="display: none;">
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" id="progressFill"></div>
                                                    </div>
                                                    <span class="progress-text" id="progressText">0%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="notice-form-actions">
                                    <button type="submit" class="btn-save-notice">
                                        <i class="fas fa-save"></i>
                                        Publish Notice
                                    </button>
                                    <button type="button" class="btn-reset-notice" id="resetNoticeBtn">
                                        <i class="fas fa-undo"></i>
                                        Reset Form
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- All Notices Display Section -->
                <div class="all-notices-container">
                    <div class="all-notices-header">
                        <h3 class="all-notices-title">
                            <i class="fas fa-list-ul"></i>
                            All Published Notices
                        </h3>
                        <div class="notices-stats">
                            <div class="stat-item">
                                <i class="fas fa-bullhorn"></i>
                                <span id="totalNotices">0</span> Total
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span id="activeNotices">0</span> Active
                            </div>
                        </div>
                    </div>
                    <div class="all-notices-content">
                        <div id="noticesTable"></div>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- Quill.js JS -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <!-- PapaParse JS for CSV handling -->
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    <script src="js/notices.js"></script>
    
    <script>
        // Initialize the admin panel for notices page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'notices';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'notices';
                console.log('Using existing AdminPanel instance for notices');
            }

            // Initialize notices manager after admin panel is ready
            setTimeout(() => {
                if (window.NoticeManager) {
                    window.noticeManager = new NoticeManager();
                }
            }, 500);
        });
    </script>
</body>
</html>
