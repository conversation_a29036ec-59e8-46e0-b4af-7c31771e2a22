<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All Timetables - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- View Timetables Section -->
            <section id="view-timetables-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title course-title">View Timetables</h1>
                    <div class="section-actions course-actions">
                        <button class="btn-import-courses" id="importTimetablesBtn">
                            <i class="fas fa-upload"></i>
                            Import Timetables
                        </button>
                        <button class="btn-export-courses" id="exportTimetablesBtn">
                            <i class="fas fa-download"></i>
                            Export Timetables
                        </button>
                        <a href="timetables.html" class="btn-view-courses">
                            <i class="fas fa-plus"></i>
                            Add New Timetable
                        </a>
                    </div>
                </div>

                <!-- Timetable Filter Section -->
                <div class="content-grid full-width">
                    <div class="course-form-container">
                        <div class="course-form-header">
                            <h3 class="course-form-title">
                                <i class="fas fa-filter"></i>
                                Filter Timetables
                            </h3>
                        </div>
                        <div class="course-form-content">
                            <div class="filter-container" style="display: flex; gap: 20px; margin-bottom: 20px; align-items: end;">
                                <div class="form-group" style="flex: 1;">
                                    <label for="courseFilter">Select Course:</label>
                                    <select id="courseFilter" class="form-control" required>
                                        <option value="">-- Select Course --</option>
                                    </select>
                                </div>
                                <div class="form-group" style="flex: 1;">
                                    <label for="semesterFilter">Select Semester:</label>
                                    <select id="semesterFilter" class="form-control" disabled>
                                        <option value="">-- Select Semester --</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button type="button" id="clearFiltersBtn" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timetables Display Section -->
                    <div class="course-form-container" id="timetablesDisplayContainer" style="display: none;">
                        <div class="course-form-header">
                            <h3 class="course-form-title" id="timetableDisplayTitle">
                                <i class="fas fa-table"></i>
                                Timetable Entries
                            </h3>
                        </div>
                        <div class="course-form-content">
                            <div class="data-list" id="timetablesList">
                                <!-- Timetables will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- Edit Timetable Modal -->
    <div id="editTimetableModal" class="course-modal-overlay" style="display: none;">
        <div class="course-modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="course-modal-header">
                <h3 class="course-modal-title">✏️ Edit Timetable Entry</h3>
                <button class="course-modal-close" onclick="TimetablesPage.closeEditModal()">&times;</button>
            </div>
            <div class="course-modal-body">
                <form id="editTimetableForm">
                    <input type="hidden" id="editTimetableId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editCourse">Course:</label>
                            <select id="editCourse" class="form-control" required>
                                <option value="">-- Select Course --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editSemester">Semester:</label>
                            <select id="editSemester" class="form-control" required>
                                <option value="">-- Select Semester --</option>
                                <option value="1">1st Semester</option>
                                <option value="2">2nd Semester</option>
                                <option value="3">3rd Semester</option>
                                <option value="4">4th Semester</option>
                                <option value="5">5th Semester</option>
                                <option value="6">6th Semester</option>
                                <option value="7">7th Semester</option>
                                <option value="8">8th Semester</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editRoomNo">Room No:</label>
                            <input type="text" id="editRoomNo" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editDay">Day:</label>
                            <select id="editDay" class="form-control" required>
                                <option value="">-- Select Day --</option>
                                <option value="Monday">Monday</option>
                                <option value="Tuesday">Tuesday</option>
                                <option value="Wednesday">Wednesday</option>
                                <option value="Thursday">Thursday</option>
                                <option value="Friday">Friday</option>
                                <option value="Saturday">Saturday</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editLecture">Lecture:</label>
                            <select id="editLecture" class="form-control" required>
                                <option value="">-- Select Lecture --</option>
                                <option value="1">1st Lecture</option>
                                <option value="2">2nd Lecture</option>
                                <option value="3">3rd Lecture</option>
                                <option value="4">4th Lecture</option>
                                <option value="5">5th Lecture</option>
                                <option value="6">6th Lecture</option>
                                <option value="7">7th Lecture</option>
                                <option value="8">8th Lecture</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editTime">Time:</label>
                            <input type="text" id="editTime" class="form-control" placeholder="e.g., 9:00 AM - 10:00 AM" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editSubject">Subject:</label>
                            <input type="text" id="editSubject" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editFaculty">Faculty:</label>
                            <input type="text" id="editFaculty" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="TimetablesPage.closeEditModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Timetable</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse for CSV handling -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    
    <!-- Courses JavaScript (includes timetable dropdown functionality) -->
    <script src="js/courses.js"></script>

    <!-- Timetables JavaScript (handles both add and view functionality) -->
    <script src="js/timetables.js"></script>
</body>
</html>
