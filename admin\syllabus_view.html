<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All Syllabus - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
    <link rel="stylesheet" href="css/syllabus.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- View Syllabus Section -->
            <section id="view-syllabus-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title syllabus-title">View All Syllabus</h1>
                     <div class="section-actions course-actions">
                        <button class="btn-import-courses" id="importSyllabusBtn">
                            <i class="fas fa-upload"></i>
                            Import Syllabus
                        </button>
                        <button class="btn-export-courses" id="exportSyllabusBtn">
                            <i class="fas fa-download"></i>
                            Export Syllabus
                        </button>
                        <a href="syllabus.html" class="btn-view-courses">
                            <i class="fas fa-plus"></i>
                            Add New Syllabus
                        </a>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="filter-section">
                    <div class="filter-container">
                        <div class="filter-group">
                            <label for="courseFilter" class="filter-label">Filter by Course:</label>
                            <select id="courseFilter" class="filter-select">
                                <option value="">All Courses</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="semesterFilter" class="filter-label">Filter by Semester:</label>
                            <select id="semesterFilter" class="filter-select">
                                <option value="">All Semesters</option>
                            </select>
                        </div>
                        <div class="filter-actions">
                            <button id="applyFilters" class="btn-apply-filter">
                                <i class="fas fa-filter"></i>
                                Apply Filters
                            </button>
                            <button id="clearFilters" class="btn-clear-filter">
                                <i class="fas fa-times"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Syllabus Display Area -->
                <div class="content-grid full-width">
                    <div class="syllabus-display-container">
                        <div id="syllabusResults" class="syllabus-results">
                            <!-- Syllabus entries will be displayed here -->
                        </div>
                        <div id="noResults" class="no-results" style="display: none;">
                            <div class="no-results-content">
                                <i class="fas fa-search"></i>
                                <h3>No Syllabus Found</h3>
                                <p>No syllabus entries match your current filters.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse JS for CSV handling -->
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    <script src="js/courses.js"></script>
    <script src="js/syllabus.js"></script>

    <script>
        // Initialize the admin panel for view syllabus page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'view-syllabus';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'view-syllabus';
                console.log('Using existing AdminPanel instance for view-syllabus');
            }

            // Initialize view syllabus page functionality
            if (window.ViewSyllabusPage && window.ViewSyllabusPage.initializeViewSyllabusPage) {
                window.ViewSyllabusPage.initializeViewSyllabusPage();
            }
        });
    </script>
</body>
</html>
