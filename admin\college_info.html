<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>College Information - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- College Info Section -->
            <section id="college-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">College Information Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="updateCollegeBtn">
                            <i class="fas fa-save"></i>
                            Update College Info
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="collegeForm" class="admin-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="collegeName">College Name *</label>
                                <input type="text" id="collegeName" name="collegeName" required>
                            </div>

                            <div class="form-group">
                                <label for="collegeType">College Type *</label>
                                <input type="text" id="collegeType" name="collegeType" placeholder="e.g., Private, Government" required>
                            </div>

                            <div class="form-group">
                                <label for="collegeLocation">Location *</label>
                                <input type="text" id="collegeLocation" name="location" required>
                            </div>

                            <div class="form-group">
                                <label for="establishmentYear">Establishment Year *</label>
                                <input type="number" id="establishmentYear" name="establishmentYear" required>
                            </div>

                            <div class="form-group">
                                <label for="contactEmail">Contact Email *</label>
                                <input type="email" id="contactEmail" name="contactEmail" required>
                            </div>

                            <div class="form-group">
                                <label for="contactPhone">Contact Phone *</label>
                                <input type="tel" id="contactPhone" name="contactPhone" required>
                            </div>

                            <div class="form-group">
                                <label for="affiliatedUniversity">Affiliated University *</label>
                                <input type="text" id="affiliatedUniversity" name="affiliatedUniversity" required>
                            </div>

                            <div class="form-group">
                                <label for="director">Director</label>
                                <input type="text" id="director" name="director">
                            </div>

                            <div class="form-group">
                                <label for="dean">Dean</label>
                                <input type="text" id="dean" name="dean">
                            </div>

                            <div class="form-group">
                                <label for="asstDirector">Assistant Director</label>
                                <input type="text" id="asstDirector" name="asstDirector">
                            </div>

                            <div class="form-group full-width">
                                <label for="facilities">Facilities</label>
                                <textarea id="facilities" name="facilities" rows="3" placeholder="List college facilities"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="eventsClubs">Events/Clubs</label>
                                <textarea id="eventsClubs" name="eventsClubs" rows="3" placeholder="List events and clubs"></textarea>
                            </div>

                            <div class="form-group full-width">
                                <label for="googleMapsLocation">Google Maps Location</label>
                                <input type="url" id="googleMapsLocation" name="googleMapsLocation" placeholder="https://maps.google.com/...">
                            </div>

                            <div class="form-group full-width">
                                <label for="hostelInfo">Hostel Information</label>
                                <textarea id="hostelInfo" name="hostelInfo" rows="3" placeholder="Hostel details and facilities"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save College Info</button>
                            <button type="button" class="btn btn-secondary" id="resetCollegeForm">Reset</button>
                        </div>
                    </form>
                </div>
            </section>
        </main>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    
    <script>
        // Initialize the admin panel for college info page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'college-info';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'college-info';
                console.log('Using existing AdminPanel instance for college-info');
            }
        });
    </script>
</body>
</html>
