/**
 * Navbar JavaScript - Handles dynamic navbar loading, theme switching and profile dropdown functionality
 * Supports both light and dark themes with smooth transitions
 */

class NavbarManager {
    constructor() {
        this.navbarLoaded = false;
        this.init();
    }

    async init() {
        await this.loadNavbar();
        this.setupElements();
        this.setupEventListeners();
        this.loadTheme();
        this.setupClickOutside();
    }

    async loadNavbar() {
        try {
            const navbarContainer = document.getElementById('navbar');
            if (!navbarContainer) {
                console.warn('Navbar container with id="navbar" not found');
                return;
            }

            // Fetch the navbar HTML
            const response = await fetch('navbar.html');
            if (!response.ok) {
                throw new Error(`Failed to load navbar: ${response.status}`);
            }

            const navbarHTML = await response.text();
            navbarContainer.innerHTML = navbarHTML;
            this.navbarLoaded = true;

            // Add a small delay to ensure DOM is updated
            await new Promise(resolve => setTimeout(resolve, 50));

        } catch (error) {
            console.error('Error loading navbar:', error);
            // Fallback: create a basic navbar structure
            this.createFallbackNavbar();
        }
    }

    createFallbackNavbar() {
        const navbarContainer = document.getElementById('navbar');
        if (!navbarContainer) return;

        navbarContainer.innerHTML = `
            <nav class="navbar" id="navbar">
                <div class="navbar-container">
                    <div class="navbar-brand">
                        <div class="brand-logo">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="brand-text">
                            <span class="brand-name">BitBot Admin</span>
                            <span class="brand-subtitle">Bhagwant Institute of Technology</span>
                        </div>
                    </div>
                    <div class="navbar-actions">
                        <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                            <i class="fas fa-sun light-icon"></i>
                            <i class="fas fa-moon dark-icon"></i>
                        </button>
                        <div class="profile-dropdown" id="profileDropdown">
                            <button class="profile-trigger" id="profileTrigger">
                                <div class="profile-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="profile-info">
                                    <span class="profile-name">Admin User</span>
                                    <span class="profile-role">Administrator</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </button>
                            <div class="dropdown-menu" id="dropdownMenu">
                                <div class="dropdown-header">
                                    <div class="dropdown-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="dropdown-info">
                                        <span class="dropdown-name">Admin User</span>
                                        <span class="dropdown-email"><EMAIL></span>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a href="#settings" class="dropdown-item" id="settingsOption">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                                <a href="#logout" class="dropdown-item logout-item" id="logoutOption">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Log Out</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        `;
        this.navbarLoaded = true;
    }

    setupElements() {
        if (!this.navbarLoaded) {
            console.warn('Navbar not loaded yet, skipping element setup');
            return;
        }

        // Theme toggle elements
        this.themeToggle = document.getElementById('themeToggle');
        this.body = document.body;

        // Profile dropdown elements
        this.profileTrigger = document.getElementById('profileTrigger');
        this.dropdownMenu = document.getElementById('dropdownMenu');
        this.profileDropdown = document.getElementById('profileDropdown');

        // Dropdown menu items
        this.settingsOption = document.getElementById('settingsOption');
        this.logoutOption = document.getElementById('logoutOption');

        // Navigation links
        this.navLinks = document.querySelectorAll('.nav-link');

        // Verify critical elements are found
        if (!this.themeToggle || !this.profileTrigger || !this.dropdownMenu) {
            console.warn('Some navbar elements not found, functionality may be limited');
        }
    }

    setupEventListeners() {
        // Theme toggle functionality
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Profile dropdown functionality
        if (this.profileTrigger) {
            this.profileTrigger.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // Dropdown menu item clicks
        if (this.settingsOption) {
            this.settingsOption.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSettingsClick();
            });
        }

        if (this.logoutOption) {
            this.logoutOption.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogoutClick();
            });
        }

        // Navigation link clicks
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavClick(link);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
    }

    setupClickOutside() {
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (this.profileDropdown && !this.profileDropdown.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }

    // Theme Management
    toggleTheme() {
        const currentTheme = this.body.classList.contains('dark-theme') ? 'dark' : 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.setTheme(newTheme);
        this.saveTheme(newTheme);
        
        // Add a subtle animation effect
        this.animateThemeChange();
    }

    setTheme(theme) {
        if (theme === 'dark') {
            this.body.classList.remove('light-theme');
            this.body.classList.add('dark-theme');
        } else {
            this.body.classList.remove('dark-theme');
            this.body.classList.add('light-theme');
        }
    }

    saveTheme(theme) {
        try {
            localStorage.setItem('navbar-theme', theme);
        } catch (error) {
            console.warn('Could not save theme preference:', error);
        }
    }

    loadTheme() {
        try {
            const savedTheme = localStorage.getItem('navbar-theme');
            if (savedTheme) {
                this.setTheme(savedTheme);
            } else {
                // Default to light theme
                this.setTheme('light');
            }
        } catch (error) {
            console.warn('Could not load theme preference:', error);
            this.setTheme('light');
        }
    }

    animateThemeChange() {
        // Add a subtle pulse effect to the theme toggle button
        if (this.themeToggle) {
            this.themeToggle.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.themeToggle.style.transform = 'scale(1)';
            }, 150);
        }
    }

    // Dropdown Management
    toggleDropdown() {
        const isOpen = this.dropdownMenu.classList.contains('show');
        
        if (isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    openDropdown() {
        this.dropdownMenu.classList.add('show');
        this.profileTrigger.classList.add('active');
        
        // Focus management for accessibility
        setTimeout(() => {
            const firstItem = this.dropdownMenu.querySelector('.dropdown-item');
            if (firstItem) {
                firstItem.focus();
            }
        }, 100);
    }

    closeDropdown() {
        this.dropdownMenu.classList.remove('show');
        this.profileTrigger.classList.remove('active');
    }

    // Navigation Management
    handleNavClick(clickedLink) {
        // Remove active class from all links
        this.navLinks.forEach(link => link.classList.remove('active'));
        
        // Add active class to clicked link
        clickedLink.classList.add('active');
        
        // Get the href attribute to determine the section
        const href = clickedLink.getAttribute('href');
        console.log(`Navigating to: ${href}`);
        
        // You can add custom navigation logic here
        // For example, showing/hiding different sections of the admin panel
        this.navigateToSection(href);
    }

    navigateToSection(section) {
        // Custom navigation logic
        // This is where you would implement the actual navigation
        // For now, we'll just log the navigation
        console.log(`Navigation to ${section} would be implemented here`);
        
        // Example: You could emit a custom event that other parts of your app can listen to
        const navigationEvent = new CustomEvent('navbar-navigate', {
            detail: { section: section }
        });
        document.dispatchEvent(navigationEvent);
    }

    // Menu Item Handlers
    handleSettingsClick() {
        console.log('Settings clicked');
        this.closeDropdown();
        
        // You can implement settings modal or navigation here
        this.showNotification('Settings panel would open here', 'info');
        
        // Example: Open settings modal or navigate to settings page
        // this.openSettingsModal();
        // or
        // window.location.href = 'settings.html';
    }

    handleLogoutClick() {
        console.log('Logout clicked');
        this.closeDropdown();
        
        // Show confirmation dialog
        this.showLogoutConfirmation();
    }

    showLogoutConfirmation() {
        // Simple confirmation dialog
        const confirmed = confirm('Are you sure you want to log out?');
        
        if (confirmed) {
            this.performLogout();
        }
    }

    performLogout() {
        // Clear any stored authentication data
        try {
            localStorage.removeItem('auth-token');
            sessionStorage.clear();
        } catch (error) {
            console.warn('Could not clear storage:', error);
        }
        
        // Show logout message
        this.showNotification('Logging out...', 'info');
        
        // Redirect to login page after a short delay
        setTimeout(() => {
            // Replace with your actual login page URL
            window.location.href = '../index.html';
        }, 1000);
    }

    // Keyboard Navigation
    handleKeydown(e) {
        // Handle Escape key to close dropdown
        if (e.key === 'Escape') {
            this.closeDropdown();
        }
        
        // Handle Enter key on profile trigger
        if (e.key === 'Enter' && e.target === this.profileTrigger) {
            this.toggleDropdown();
        }
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        // Simple notification system
        // You can replace this with your preferred notification library
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // If you have a notification system like Toastify, you can use it here
        // Example:
        // if (typeof Toastify !== 'undefined') {
        //     Toastify({
        //         text: message,
        //         duration: 3000,
        //         gravity: "top",
        //         position: "center",
        //         className: `toast-${type}`
        //     }).showToast();
        // }
    }

    // Public API Methods
    getCurrentTheme() {
        return this.body.classList.contains('dark-theme') ? 'dark' : 'light';
    }

    setActiveNavItem(href) {
        this.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === href) {
                link.classList.add('active');
            }
        });
    }

    updateProfileInfo(name, role, email) {
        // Update profile information in the navbar
        const profileName = document.querySelector('.profile-name');
        const profileRole = document.querySelector('.profile-role');
        const dropdownName = document.querySelector('.dropdown-name');
        const dropdownEmail = document.querySelector('.dropdown-email');
        
        if (profileName) profileName.textContent = name;
        if (profileRole) profileRole.textContent = role;
        if (dropdownName) dropdownName.textContent = name;
        if (dropdownEmail) dropdownEmail.textContent = email;
    }
}

// Initialize the navbar when the DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        window.navbarManager = new NavbarManager();
    } catch (error) {
        console.error('Failed to initialize navbar:', error);
    }
});

// Utility function to load navbar into any page
window.loadNavbar = async function() {
    if (!window.navbarManager) {
        window.navbarManager = new NavbarManager();
    }
    return window.navbarManager;
};

// Export for use in other modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavbarManager;
}
