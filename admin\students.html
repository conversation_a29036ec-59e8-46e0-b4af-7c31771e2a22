<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Students Section -->
            <section id="students-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Student Data Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchStudents">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addStudentBtn">
                            <i class="fas fa-plus"></i>
                            Add Student
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="studentForm" class="admin-form">
                        <input type="hidden" id="studentId" name="studentId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="rollNo">Roll No *</label>
                                <input type="text" id="rollNo" name="rollNo" required>
                            </div>

                            <div class="form-group">
                                <label for="enrollmentNo">Enrollment No *</label>
                                <input type="text" id="enrollmentNo" name="enrollmentNo" required>
                            </div>

                            <div class="form-group">
                                <label for="studentName">Student Name *</label>
                                <input type="text" id="studentName" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="fatherName">Father's Name</label>
                                <input type="text" id="fatherName" name="fatherName">
                            </div>

                            <div class="form-group">
                                <label for="motherName">Mother's Name</label>
                                <input type="text" id="motherName" name="motherName">
                            </div>

                            <div class="form-group">
                                <label for="studentPhone">Phone *</label>
                                <input type="tel" id="studentPhone" name="phone" required>
                            </div>

                            <div class="form-group">
                                <label for="studentEmail">Email *</label>
                                <input type="email" id="studentEmail" name="email" required>
                            </div>

                            <div class="form-group">
                                <label for="studentCourse">Course *</label>
                                <select id="studentCourse" name="course" required>
                                    <option value="">Select Course</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="studentBranch">Branch</label>
                                <input type="text" id="studentBranch" name="branch">
                            </div>

                            <div class="form-group">
                                <label for="studentYear">Year *</label>
                                <select id="studentYear" name="year" required>
                                    <option value="">Select Year</option>
                                    <option value="1st Year">1st Year</option>
                                    <option value="2nd Year">2nd Year</option>
                                    <option value="3rd Year">3rd Year</option>
                                    <option value="4th Year">4th Year</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="studentSemester">Semester</label>
                                <select id="studentSemester" name="semester">
                                    <option value="">Select Semester</option>
                                    <option value="1st Semester">1st Semester</option>
                                    <option value="2nd Semester">2nd Semester</option>
                                    <option value="3rd Semester">3rd Semester</option>
                                    <option value="4th Semester">4th Semester</option>
                                    <option value="5th Semester">5th Semester</option>
                                    <option value="6th Semester">6th Semester</option>
                                    <option value="7th Semester">7th Semester</option>
                                    <option value="8th Semester">8th Semester</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="dateOfBirth">Date of Birth</label>
                                <input type="date" id="dateOfBirth" name="dateOfBirth">
                            </div>

                            <div class="form-group full-width">
                                <label for="address">Address</label>
                                <textarea id="address" name="address" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Student</button>
                            <button type="button" class="btn btn-secondary" id="resetStudentForm">Reset</button>
                        </div>
                    </form>
                </div>

                <!-- Search and Filter Controls -->
                <div class="table-controls" style="margin-bottom: 20px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                    <div class="search-group" style="flex: 1; min-width: 250px;">
                        <input type="text" id="studentSearch" placeholder="Search students..." style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div class="filter-group" style="display: flex; gap: 10px;">
                        <select id="courseFilter" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="">All Courses</option>
                        </select>
                        <select id="yearFilter" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="">All Years</option>
                            <option value="1st Year">1st Year</option>
                            <option value="2nd Year">2nd Year</option>
                            <option value="3rd Year">3rd Year</option>
                            <option value="4th Year">4th Year</option>
                        </select>
                        <button id="clearFilters" class="btn btn-outline" style="padding: 8px 16px;">Clear Filters</button>
                    </div>
                </div>

                <!-- Tabulator Table Container -->
                <div id="studentsTable" style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
                    <!-- Tabulator will be initialized here -->
                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    
    <script>
        // Initialize the admin panel for students page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'students';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'students';
                console.log('Using existing AdminPanel instance for students');
            }
        });
    </script>
</body>
</html>
