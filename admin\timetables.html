<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timetable Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
</head>
<body class="light-theme">
        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Time Tables Section -->
            <section id="timetables-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title course-title">Timetable Management</h1>
                    <div class="section-actions course-actions">
                        <a href="view-timetables.html" class="btn-view-courses">
                            <i class="fas fa-eye"></i>
                            View All Timetables
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="course-form-container">
                        <div class="course-form-header">
                            <h3 class="course-form-title">
                                <i class="fas fa-calendar-alt"></i>
                                Timetable Entry Information
                            </h3>
                        </div>
                        <div class="course-form-content">
                            <form id="timetableForm" class="modern-form">
                                <input type="hidden" id="timetableId" name="timetableId">

                                <div class="course-form-grid">
                                    <div class="course-form-group">
                                        <label class="course-form-label" for="timetableCourse">📚 Course *</label>
                                        <select class="course-form-input" id="timetableCourse" name="course" required>
                                            <option value="">Select Course</option>
                                        </select>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="semester">📖 Semester *</label>
                                        <select class="course-form-input" id="semester" name="semester" required>
                                            <option value="">Select Semester</option>
                                            <option value="1st">1st Semester</option>
                                            <option value="2nd">2nd Semester</option>
                                            <option value="3rd">3rd Semester</option>
                                            <option value="4th">4th Semester</option>
                                            <option value="5th">5th Semester</option>
                                            <option value="6th">6th Semester</option>
                                            <option value="7th">7th Semester</option>
                                            <option value="8th">8th Semester</option>
                                        </select>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="roomNo">🏢 Room No *</label>
                                        <input class="course-form-input" type="text" id="roomNo" name="roomNo" placeholder="e.g., Room 101, Lab A" required>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="day">📅 Day *</label>
                                        <select class="course-form-input" id="day" name="day" required>
                                            <option value="">Select Day</option>
                                            <option value="Monday">Monday</option>
                                            <option value="Tuesday">Tuesday</option>
                                            <option value="Wednesday">Wednesday</option>
                                            <option value="Thursday">Thursday</option>
                                            <option value="Friday">Friday</option>
                                            <option value="Saturday">Saturday</option>
                                            <option value="Sunday">Sunday</option>
                                        </select>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="lecture">🔢 Lecture *</label>
                                        <select class="course-form-input" id="lecture" name="lecture" required>
                                            <option value="">Select Lecture</option>
                                            <option value="1st">1st Lecture</option>
                                            <option value="2nd">2nd Lecture</option>
                                            <option value="3rd">3rd Lecture</option>
                                            <option value="4th">4th Lecture</option>
                                            <option value="5th">5th Lecture</option>
                                            <option value="6th">6th Lecture</option>
                                            <option value="7th">7th Lecture</option>
                                            <option value="8th">8th Lecture</option>
                                            <option value="9th">9th Lecture</option>
                                            <option value="10th">10th Lecture</option>
                                        </select>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="subject">📝 Subject *</label>
                                        <input class="course-form-input" type="text" id="subject" name="subject" placeholder="e.g., Mathematics, Computer Science" required>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="faculty">👨‍🏫 Faculty *</label>
                                        <input class="course-form-input" type="text" id="faculty" name="faculty" placeholder="e.g., Dr. John Smith" required>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="time">⏰ Time *</label>
                                        <input class="course-form-input" type="text" id="time" name="time" placeholder="e.g., 09:00 AM - 10:00 AM" required>
                                    </div>
                                </div>

                                <div class="course-form-actions">
                                    <button type="submit" class="btn-save-course">
                                        <i class="fas fa-save"></i>
                                        Save Timetable Entry
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>


            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>

    <!-- Courses JavaScript (includes timetable dropdown functionality) -->
    <script src="js/courses.js"></script>

    <!-- Timetables JavaScript -->
    <script src="js/timetables.js"></script>
</body>
</html>
