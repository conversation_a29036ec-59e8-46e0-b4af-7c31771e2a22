/**
 * Navbar JavaScript - Handles theme switching and profile dropdown functionality
 * Supports both light and dark themes with smooth transitions
 */

class NavbarManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupElements();
        this.setupEventListeners();
        this.loadTheme();
        this.setupClickOutside();
    }

    setupElements() {
        // Theme toggle elements
        this.themeToggle = document.getElementById('themeToggle');
        this.body = document.body;

        // Profile dropdown elements
        this.profileTrigger = document.getElementById('profileTrigger');
        this.dropdownMenu = document.getElementById('dropdownMenu');
        this.profileDropdown = document.getElementById('profileDropdown');

        // Dropdown menu items
        this.settingsOption = document.getElementById('settingsOption');
        this.logoutOption = document.getElementById('logoutOption');

        // Navigation links
        this.navLinks = document.querySelectorAll('.nav-link');
    }

    setupEventListeners() {
        // Theme toggle functionality
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Profile dropdown functionality
        if (this.profileTrigger) {
            this.profileTrigger.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // Dropdown menu item clicks
        if (this.settingsOption) {
            this.settingsOption.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSettingsClick();
            });
        }

        if (this.logoutOption) {
            this.logoutOption.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogoutClick();
            });
        }

        // Navigation link clicks
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavClick(link);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
    }

    setupClickOutside() {
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (this.profileDropdown && !this.profileDropdown.contains(e.target)) {
                this.closeDropdown();
            }
        });
    }

    // Theme Management
    toggleTheme() {
        const currentTheme = this.body.classList.contains('dark-theme') ? 'dark' : 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.setTheme(newTheme);
        this.saveTheme(newTheme);
        
        // Add a subtle animation effect
        this.animateThemeChange();
    }

    setTheme(theme) {
        if (theme === 'dark') {
            this.body.classList.remove('light-theme');
            this.body.classList.add('dark-theme');
        } else {
            this.body.classList.remove('dark-theme');
            this.body.classList.add('light-theme');
        }
    }

    saveTheme(theme) {
        try {
            localStorage.setItem('navbar-theme', theme);
        } catch (error) {
            console.warn('Could not save theme preference:', error);
        }
    }

    loadTheme() {
        try {
            const savedTheme = localStorage.getItem('navbar-theme');
            if (savedTheme) {
                this.setTheme(savedTheme);
            } else {
                // Default to light theme
                this.setTheme('light');
            }
        } catch (error) {
            console.warn('Could not load theme preference:', error);
            this.setTheme('light');
        }
    }

    animateThemeChange() {
        // Add a subtle pulse effect to the theme toggle button
        if (this.themeToggle) {
            this.themeToggle.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.themeToggle.style.transform = 'scale(1)';
            }, 150);
        }
    }

    // Dropdown Management
    toggleDropdown() {
        const isOpen = this.dropdownMenu.classList.contains('show');
        
        if (isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    openDropdown() {
        this.dropdownMenu.classList.add('show');
        this.profileTrigger.classList.add('active');
        
        // Focus management for accessibility
        setTimeout(() => {
            const firstItem = this.dropdownMenu.querySelector('.dropdown-item');
            if (firstItem) {
                firstItem.focus();
            }
        }, 100);
    }

    closeDropdown() {
        this.dropdownMenu.classList.remove('show');
        this.profileTrigger.classList.remove('active');
    }

    // Navigation Management
    handleNavClick(clickedLink) {
        // Remove active class from all links
        this.navLinks.forEach(link => link.classList.remove('active'));
        
        // Add active class to clicked link
        clickedLink.classList.add('active');
        
        // Get the href attribute to determine the section
        const href = clickedLink.getAttribute('href');
        console.log(`Navigating to: ${href}`);
        
        // You can add custom navigation logic here
        // For example, showing/hiding different sections of the admin panel
        this.navigateToSection(href);
    }

    navigateToSection(section) {
        // Custom navigation logic
        // This is where you would implement the actual navigation
        // For now, we'll just log the navigation
        console.log(`Navigation to ${section} would be implemented here`);
        
        // Example: You could emit a custom event that other parts of your app can listen to
        const navigationEvent = new CustomEvent('navbar-navigate', {
            detail: { section: section }
        });
        document.dispatchEvent(navigationEvent);
    }

    // Menu Item Handlers
    handleSettingsClick() {
        console.log('Settings clicked');
        this.closeDropdown();
        
        // You can implement settings modal or navigation here
        this.showNotification('Settings panel would open here', 'info');
        
        // Example: Open settings modal or navigate to settings page
        // this.openSettingsModal();
        // or
        // window.location.href = 'settings.html';
    }

    handleLogoutClick() {
        console.log('Logout clicked');
        this.closeDropdown();
        
        // Show confirmation dialog
        this.showLogoutConfirmation();
    }

    showLogoutConfirmation() {
        // Simple confirmation dialog
        const confirmed = confirm('Are you sure you want to log out?');
        
        if (confirmed) {
            this.performLogout();
        }
    }

    performLogout() {
        // Clear any stored authentication data
        try {
            localStorage.removeItem('auth-token');
            sessionStorage.clear();
        } catch (error) {
            console.warn('Could not clear storage:', error);
        }
        
        // Show logout message
        this.showNotification('Logging out...', 'info');
        
        // Redirect to login page after a short delay
        setTimeout(() => {
            // Replace with your actual login page URL
            window.location.href = '../index.html';
        }, 1000);
    }

    // Keyboard Navigation
    handleKeydown(e) {
        // Handle Escape key to close dropdown
        if (e.key === 'Escape') {
            this.closeDropdown();
        }
        
        // Handle Enter key on profile trigger
        if (e.key === 'Enter' && e.target === this.profileTrigger) {
            this.toggleDropdown();
        }
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        // Simple notification system
        // You can replace this with your preferred notification library
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // If you have a notification system like Toastify, you can use it here
        // Example:
        // if (typeof Toastify !== 'undefined') {
        //     Toastify({
        //         text: message,
        //         duration: 3000,
        //         gravity: "top",
        //         position: "center",
        //         className: `toast-${type}`
        //     }).showToast();
        // }
    }

    // Public API Methods
    getCurrentTheme() {
        return this.body.classList.contains('dark-theme') ? 'dark' : 'light';
    }

    setActiveNavItem(href) {
        this.navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === href) {
                link.classList.add('active');
            }
        });
    }

    updateProfileInfo(name, role, email) {
        // Update profile information in the navbar
        const profileName = document.querySelector('.profile-name');
        const profileRole = document.querySelector('.profile-role');
        const dropdownName = document.querySelector('.dropdown-name');
        const dropdownEmail = document.querySelector('.dropdown-email');
        
        if (profileName) profileName.textContent = name;
        if (profileRole) profileRole.textContent = role;
        if (dropdownName) dropdownName.textContent = name;
        if (dropdownEmail) dropdownEmail.textContent = email;
    }
}

// Initialize the navbar when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navbarManager = new NavbarManager();
});

// Export for use in other modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavbarManager;
}
